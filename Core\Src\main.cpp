#include "common_inc.h"

TB67H450 tb67h450;
TB67H450Base tb67h450base;
EncoderCalibratorBase encoderCalibratorBase;

void TIM1_Callback_10ms(void);
void TIM4_Callback_50ms(void);

/*
fabs测试
*/


bool flag = 1;
uint32_t goPosition = 0;



uint16_t SpiTransmitAndRead16Bits(uint16_t _dataTx);
uint16_t rawData = 0;
uint8_t checksumFlag = 0;
uint16_t rawAngle = 0;
bool noMagFlag = 0;
uint16_t dataRx[2] = {0};
uint16_t dataTx[2] = {0};
uint16_t rectifiedAngle = 0;

void Main()
{
    tb67h450.InitGpio();
    tb67h450.InitPwm();


    __HAL_TIM_CLEAR_IT(&htim1, TIM_IT_UPDATE);
    __HAL_TIM_CLEAR_IT(&htim4, TIM_IT_UPDATE);
    HAL_Delay(10);
    HAL_TIM_Base_Start_IT(&htim1);
    HAL_TIM_Base_Start_IT(&htim4);
    HAL_TIM_PWM_Start(&htim2, TIM_CHANNEL_3);
    HAL_TIM_PWM_Start(&htim2, TIM_CHANNEL_4);
    for(;;){
    }
}


uint16_t SpiTransmitAndRead16Bits(uint16_t _dataTx)
{
    uint16_t dataRx;

    GPIOA->BRR = GPIO_PIN_15; // Chip select
    HAL_SPI_TransmitReceive(&hspi1, (uint8_t *)&_dataTx, (uint8_t *)&dataRx, 1, HAL_MAX_DELAY);
    GPIOA->BSRR = GPIO_PIN_15;

    return dataRx;
}

// 默认2000mA每相的测试电流
void motor_run(u_int32_t _position)
{
    tb67h450.SetFocCurrentVector1(_position,2000);
}

void mt6816_get_raw_data(void)
{
    dataTx[0] = (0x80 | 0x03) << 8; //0x8300
    dataTx[1] = (0x80 | 0x04) << 8; //0x8400

    for(uint8_t i = 0;i < 3; i++){
        dataRx[0] = SpiTransmitAndRead16Bits(dataTx[0]);
        dataRx[1] = SpiTransmitAndRead16Bits(dataTx[1]);

        rawData = ((dataRx[0] & 0x00FF) << 8) |(dataRx[1] & 0x00FF);
        //奇偶校驗
        uint8_t h_count = 0;
        for(uint8_t j = 0;j < 16; j++){
            if(rawData & (0x0001 << j))
                h_count++;
        }
        if(h_count & 0x01){
            checksumFlag =false;         
        } 
        else{ //如果是偶数（即 1 的个数为偶数）
             checksumFlag = true; 
             break;
        }
    }

    if(checksumFlag)
    {
        rawAngle = rawData >> 2; // 前面 14 Bits 的angle數據
        noMagFlag = (bool)(rawData & (0x0001 << 1));
        //弱磁標誌位
    }
}


void HAL_TIM_PeriodElapsedCallback(TIM_HandleTypeDef *htim)
{
    if(htim == (&htim1)){
        TIM1_Callback_10ms();
    }
    if(htim == (&htim4)){
        TIM4_Callback_50ms();
    }

}

void TIM1_Callback_10ms(void)
{
}

void TIM4_Callback_50ms(void)
{
    if (goPosition <= 51200)
    {
        // 测试：以2000mA电流跑一圈
        tb67h450.SetFocCurrentVector1(goPosition, 1000);
        goPosition += 2;
    }
}