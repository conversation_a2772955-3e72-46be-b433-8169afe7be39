{"name": "Dummy_motor_spi_0515", "type": "ARM", "dependenceList": [], "srcDirs": ["Core", "Drivers"], "virtualFolder": {"name": "<virtual_root>", "files": [], "folders": []}, "outDir": "build", "deviceName": null, "packDir": null, "miscInfo": {"uid": "72768406de51ec75e4d2fe8dbd86ca00"}, "targets": {"Debug": {"excludeList": [], "toolchain": "GCC", "compileConfig": {"cpuType": "Cortex-M3", "floatingPointHardware": "none", "scatterFilePath": "STM32F103C8TX_FLASH.ld", "useCustomScatterFile": true, "storageLayout": {"RAM": [], "ROM": []}, "options": "null"}, "uploader": "OpenOCD", "uploadConfig": {"bin": "", "target": "stm32f1x", "interface": "stlink", "baseAddr": "0x08000000"}, "uploadConfigMap": {"JLink": {"bin": "", "baseAddr": "", "cpuInfo": {"vendor": "null", "cpuName": "null"}, "proType": 1, "speed": 8000, "otherCmds": ""}}, "custom_dep": {"name": "default", "incList": ["Core/Inc", "Drivers/STM32F1xx_HAL_Driver/Inc", "Drivers/STM32F1xx_HAL_Driver/Inc/Legacy", "Drivers/CMSIS/Device/ST/STM32F1xx/Include", "Drivers/CMSIS/Include"], "libList": [], "defineList": ["DEBUG", "USE_HAL_DRIVER", "STM32F103xB"]}, "builderOptions": {"GCC": {"version": 5, "beforeBuildTasks": [], "afterBuildTasks": [], "global": {"$float-abi-type": "softfp", "output-debug-info": "disable", "misc-control": []}, "c/cpp-compiler": {"language-c": "c11", "language-cpp": "c++11", "optimization": "level-size", "warnings": "all-warnings", "one-elf-section-per-function": true, "one-elf-section-per-data": true, "C_FLAGS": "", "CXX_FLAGS": ""}, "asm-compiler": {"ASM_FLAGS": ""}, "linker": {"output-format": "elf", "remove-unused-input-sections": true, "LD_FLAGS": "-TSTM32F103C8TX_FLASH.ld", "LIB_FLAGS": ""}}}}, "Release": {"excludeList": [], "toolchain": "GCC", "compileConfig": {"cpuType": "Cortex-M3", "floatingPointHardware": "none", "scatterFilePath": "STM32F103C8TX_FLASH.ld", "useCustomScatterFile": true, "storageLayout": {"RAM": [], "ROM": []}, "options": "null"}, "uploader": "OpenOCD", "uploadConfig": {"bin": "", "target": "stm32f1x", "interface": "stlink", "baseAddr": "0x08000000"}, "uploadConfigMap": {"JLink": {"bin": "", "baseAddr": "", "cpuInfo": {"vendor": "null", "cpuName": "null"}, "proType": 1, "speed": 8000, "otherCmds": ""}}, "builderOptions": {"GCC": {"version": 5, "beforeBuildTasks": [], "afterBuildTasks": [], "global": {"$float-abi-type": "softfp", "output-debug-info": "disable", "misc-control": []}, "c/cpp-compiler": {"language-c": "c11", "language-cpp": "c++11", "optimization": "level-size", "warnings": "all-warnings", "one-elf-section-per-function": true, "one-elf-section-per-data": true, "C_FLAGS": "", "CXX_FLAGS": ""}, "asm-compiler": {"ASM_FLAGS": ""}, "linker": {"output-format": "elf", "remove-unused-input-sections": true, "LD_FLAGS": "-TSTM32F103C8TX_FLASH.ld", "LIB_FLAGS": ""}}}, "custom_dep": {"name": "default", "incList": ["Core/Inc", "Drivers/STM32F1xx_HAL_Driver/Inc", "Drivers/STM32F1xx_HAL_Driver/Inc/Legacy", "Drivers/CMSIS/Device/ST/STM32F1xx/Include", "Drivers/CMSIS/Include"], "defineList": ["USE_HAL_DRIVER", "STM32F103xB"], "libList": []}}}, "version": "3.5"}