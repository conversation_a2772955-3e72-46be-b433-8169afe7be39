#include "encoder_calibrator_base.h"

uint32_t goposition = 0;

extern TB67H450 tb67h450;

int32_t EncoderCalibratorBase::CycleDataAverage(const uint16_t *_data,
                                                uint16_t _length,int32_t _cyc)
{
    int32_t sumData = 0;
    int32_t subData;
    int32_t diffData;

    sumData += (int32_t)_data[0];
    for(uint16_t i = 1;i < _length; i++ ){
        diffData = (int32_t)_data[i];
        subData = (int32_t)_data[i] - (int32_t)_data[0];
        if (subData > (_cyc >> 1))
            diffData = (int32_t)_data[i] - _cyc;
        if (subData < (-_cyc >> 1))
            diffData = (int32_t)_data[i] + _cyc;
        sumData += diffData;
    }

    sumData = sumData / _length;
    
    if(sumData < 0)
        sumData += _cyc;

    if(sumData > _cyc)
        sumData -= _cyc;

    return sumData;
}

// // 注意电流超过1A
void EncoderCalibratorBase::Tick20kHz()
{
    static bool _a = 1;

    if(_a){
        state = CALI_DISABLE;
        isTriggered = 1;
        _a = 0;
    }

    switch (state)
    {
    case CALI_DISABLE:
        if (isTriggered)
        {
            tb67h450.SetFocCurrentVector(goPosition, 2000);
            goPosition = 51200;
            sampleCount = 0;
            state = CALI_FORWARD_PREPARE;
            errorCode = CALI_NO_ERROR;
        }       
        break;
    case CALI_FORWARD_PREPARE: // 細分2，正轉一圈
        goPosition += AUTO_CALIB_SPEED; //
        tb67h450.SetFocCurrentVector(goPosition, 2000);
        if (goPosition == 2 * 51200)
        {
            goPosition = 51200;
            state = CALI_FORWARD_MEASURE;
        }
        break;
    case CALI_FORWARD_MEASURE:  // 每1.8度记录一个数据，再正转一圈
        if ((goPosition % 256) == 0)
        {
            goPosition += FINE_TUNE_CALIB_SPEED;
        }
        else{
            goPosition += FINE_TUNE_CALIB_SPEED;
        }
        tb67h450.SetFocCurrentVector(goPosition, 2000);

        if (goPosition > (2 * 51200)){
            state = CALI_BACKWARD_RETURN;
        }
        break;
    case CALI_BACKWARD_RETURN:  // 再正转20个1.8度
        goPosition += FINE_TUNE_CALIB_SPEED;
        tb67h450.SetFocCurrentVector(goPosition,2000);

        if(goPosition == (2*51200 + 256 *20)){
            state = CALI_BACKWARD_GAP_DISMISS;
        }
        break;
    case CALI_BACKWARD_GAP_DISMISS: //反轉20个1.8度
         goPosition -= FINE_TUNE_CALIB_SPEED;
        tb67h450.SetFocCurrentVector(goPosition,2000);

        if(goPosition == (2*51200)){
            state = CALI_BACKWARD_MEASURE;
        }
        break;
    case CALI_BACKWARD_MEASURE: // 反转一圈
        if ((goPosition % 256) == 0)
        {
            // sampleDataRaw[sampleCount++] = motor->encoder->angleData.rawAngle;
            // if (sampleCount == EncoderCalibratorBase::SAMPLE_COUNTS_PER_STEP)
            // {
            //     sampleDataAverageBackward[(goPosition - 51200) /
            //                               256] = CycleDataAverage(sampleDataRaw, EncoderCalibratorBase::SAMPLE_COUNTS_PER_STEP,
            //                                                       16384);

            //     sampleCount = 0;
            goPosition -= FINE_TUNE_CALIB_SPEED;
            // }
        }
        else
        {
            goPosition -= FINE_TUNE_CALIB_SPEED;
        }
        tb67h450.SetFocCurrentVector(goPosition, 2000);
        if (goPosition < 51200)
        {
            state = CALI_CALCULATING;
        }
        break;
    case CALI_CALCULATING:
        tb67h450.SetFocCurrentVector(0, 0);
        break;
    default:
        break;
    }
}

// 进行步距检测，通过后得到零点前的最后一步步数及零点后
// 第一步与零点的编码器距离
void EncoderCalibratorBase::CalibrationDataCheck()
{
    uint32_t count;
    int32_t subData;
    //MOTOR_ONE_CIRCLE_HARD_STEPS is 200 step , 360/200 1.8*
    //Resolution is (2^14 = 16384)
    int32_t calibSampleResolution = mt6816_base.RESOLUTION / MOTOR_ONE_CIRCLE_HARD_STEPS; // 81
    /******************** 检查平均值连续性及方向 ********************/
    //求解平均值数据
    for(count = 0;count < MOTOR_ONE_CIRCLE_HARD_STEPS + 1; count++){
        // 由正转值和反转值再求平均值，存放在正转数组里
        sampleDataAverageForward[count] = (uint16_t)CycleAverage((int32_t)sampleDataAverageForward[count],
                                                                (int32_t)sampleDataAverageBackward[count],
                                                                 mt6816_base.RESOLUTION);
    }
    subData = CycleSubtract((int32_t)sampleDataAverageForward[0],
                            (int32_t)sampleDataAverageForward[MOTOR_ONE_CIRCLE_HARD_STEPS - 1],
                            mt6816_base.RESOLUTION); // 由第一次和最后一次的数据的差值得到正负值代表方向
    //旋转的方向性确认 
    if(subData == 0){
        errorCode = CALI_ERROR_AVERAGE_DIR;
        return;
    }
    else{
        goDirection = subData > 0;// 1代表顺时针
    }
    //再次对电机每一步的编码器数据进行检查：
    // 正向檢查  後步 跟 前步
    for(count = 1 ; count < MOTOR_ONE_CIRCLE_HARD_STEPS ;count ++ ) {
        subData =CycleSubtract((int32_t) sampleDataAverageForward[count],
                                (int32_t)sampleDataAverageForward[count-1],
                                         motor->encoder->RESOLUTION);
        if (fabs(subData) > (calibSampleResolution * 3 / 2)) // delta-data too large，121
        {
            errorCode = CALI_ERROR_AVERAGE_CONTINUTY;
            return;
        }
        if (fabs(subData) < (calibSampleResolution * 1 / 2)) // delta-data too small，40
        {
            errorCode = CALI_ERROR_AVERAGE_CONTINUTY;
            return;
        }
        if (subData == 0)
        {
            errorCode = CALI_ERROR_AVERAGE_DIR;
            return;
        }
        if ((subData > 0) && (!goDirection)) // 方向不同
        {
            errorCode = CALI_ERROR_AVERAGE_DIR;
            return;
        }
        if ((subData < 0) && (goDirection))
        {
            errorCode = CALI_ERROR_AVERAGE_DIR;
            return;
        }
    /******************** 全段域校准 完全擬合传感器数据与电机实际相位角非线性关系 ********************/
    //寻找区间下标及阶跃差值
        // 200步 data 全部通過，零點判斷
        uint32_t step_num = 0;
        if (goDirection) // 顺时针
        {
            for(count = 0;count < MOTOR_ONE_CIRCLE_HARD_STEPS;count++)
            {
                // 正向檢查  後步 跟 前步
                subData = (int32_t)sampleDataAverageForward[CycleMod(count + 1,MOTOR_ONE_CIRCLE_HARD_STEPS)] -
                          (int32_t)sampleDataAverageForward[CycleMod(count , MOTOR_ONE_CIRCLE_HARD_STEPS)];
                if(subData < 0) //當有一次穿過時鐘零點
                {
                    step_num++;
                    rcdX = (int32_t)count;// 记录零点对应步数位置
                    rcdY = (mt6816_base.RESOLUTION - 1) -
                           sampleDataAverageForward[CycleMod(rcdX, MOTOR_ONE_CIRCLE_HARD_STEPS)]; // 16383-零点对应的mt6816校准数据
                }
            }
            if(step_num != 1){
                errorCode =CALI_ERROR_PHASE_STEP;
                return;
            }
        }
        else{ //逆時針
            for (count = 0; count < MOTOR_ONE_CIRCLE_HARD_STEPS; count++)
            {
                subData = (int32_t)sampleDataAverageForward[CycleMod(count + 1, MOTOR_ONE_CIRCLE_HARD_STEPS)] -
                          (int32_t)sampleDataAverageForward[CycleMod(count, MOTOR_ONE_CIRCLE_HARD_STEPS)];
                if (subData > 0){
                    step_num++;
                    rcdX = (int32_t)count;
                    // +1 ??
                    rcdY = (mt6816_base.RESOLUTION - 1) -
                       sampleDataAverageForward[CycleMod(rcdX + 1, MOTOR_ONE_CIRCLE_HARD_STEPS)];
                }
            }
            if(step_num != 1){
                errorCode =CALI_ERROR_PHASE_STEP;
                return;
            }
        }
         errorCode = CALI_NO_ERROR;
    }

}

void EncoderCalibratorBase::TickMainLoop()
{
    int32_t dataI32;   // 每相鄰兩步數據差
    uint16_t dataU16;  // 對應细分数

    if (state != CALI_CALCULATING){  // 完成CALI
        return;
    }

    tb67h450base.Sleep();

    CalibrationDataCheck();

}

uint32_t EncoderCalibratorBase::CycleMod(uint32_t _a,uint32_t _b)
{
    return (_a + _b) % _b;
}

int32_t EncoderCalibratorBase::CycleSubtract(int32_t _a, int32_t _b, int32_t _cyc)
{
    int32_t sub_data;

    sub_data = _a - _b;
    if (sub_data > (_cyc >> 1))
        sub_data -= _cyc;
    if (sub_data < (-_cyc >> 1))
        sub_data += _cyc;
    return sub_data;
}


int32_t EncoderCalibratorBase::CycleAverage(int32_t _a,int32_t _b, int32_t _cyc)
{
    int32_t sub_data;
    int32_t avg_data;


    sub_data = _a - _b;
    avg_data = (_a + _b) >> 1; 

    if(fabs(sub_data) > (_cyc >>1)) //跳變判斷
    {
        if (ave_data >= (_cyc >> 1))
            ave_data -= (_cyc >> 1);
        else
            ave_data += (_cyc >> 1);
    } 

    return ave_data;
}