#include "encoder_calibrator_base.h"
#include "math.h"

uint32_t goposition = 0;

extern TB67H450 tb67h450;

int32_t EncoderCalibratorBase::CycleDataAverage(const uint16_t *_data,
                                                uint16_t _length,int32_t _cyc)
{
    int32_t sumData = 0;
    int32_t subData;
    int32_t diffData;

    sumData += (int32_t)_data[0];
    for(uint16_t i = 1;i < _length; i++ ){
        diffData = (int32_t)_data[i];
        subData = (int32_t)_data[i] - (int32_t)_data[0];
        if (subData > (_cyc >> 1))
            diffData = (int32_t)_data[i] - _cyc;
        if (subData < (-_cyc >> 1))
            diffData = (int32_t)_data[i] + _cyc;
        sumData += diffData;
    }

    sumData = sumData / _length;
    
    if(sumData < 0)
        sumData += _cyc;

    if(sumData > _cyc)
        sumData -= _cyc;

    return sumData;
}

// // 注意电流超过1A
void EncoderCalibratorBase::Tick20kHz()
{
    static bool _a = 1;

    if(_a){
        state = CALI_DISABLE;
        isTriggered = 1;
        _a = 0;
    }

    switch (state)
    {
    case CALI_DISABLE:
        if (isTriggered)
        {
            tb67h450.SetFocCurrentVector(goPosition, 2000);
            goPosition = 51200;
            sampleCount = 0;
            state = CALI_FORWARD_PREPARE;
            errorCode = CALI_NO_ERROR;
        }       
        break;
    case CALI_FORWARD_PREPARE: // 細分2，正轉一圈
        goPosition += AUTO_CALIB_SPEED; //
        tb67h450.SetFocCurrentVector(goPosition, 2000);
        if (goPosition == 2 * 51200)
        {
            goPosition = 51200;
            state = CALI_FORWARD_MEASURE;
        }
        break;
    case CALI_FORWARD_MEASURE:  // 每1.8度记录一个数据，再正转一圈
        if ((goPosition % 256) == 0)
        {
            goPosition += FINE_TUNE_CALIB_SPEED;
        }
        else{
            goPosition += FINE_TUNE_CALIB_SPEED;
        }
        tb67h450.SetFocCurrentVector(goPosition, 2000);

        if (goPosition > (2 * 51200)){
            state = CALI_BACKWARD_RETURN;
        }
        break;
    case CALI_BACKWARD_RETURN:  // 再正转20个1.8度
        goPosition += FINE_TUNE_CALIB_SPEED;
        tb67h450.SetFocCurrentVector(goPosition,2000);

        if(goPosition == (2*51200 + 256 *20)){
            state = CALI_BACKWARD_GAP_DISMISS;
        }
        break;
    case CALI_BACKWARD_GAP_DISMISS: //反轉20个1.8度
        goPosition -= FINE_TUNE_CALIB_SPEED;
        tb67h450.SetFocCurrentVector(goPosition,2000);

        if(goPosition == (2 * 51200)){
            state = CALI_BACKWARD_MEASURE;
        }
        break;
    case CALI_BACKWARD_MEASURE: // 反转一圈
        if ((goPosition % 256) == 0)
        {
            // sampleDataRaw[sampleCount++] = motor->encoder->angleData.rawAngle;
            // if (sampleCount == EncoderCalibratorBase::SAMPLE_COUNTS_PER_STEP)
            // {
            //     sampleDataAverageBackward[(goPosition - 51200) /
            //                               256] = CycleDataAverage(sampleDataRaw, EncoderCalibratorBase::SAMPLE_COUNTS_PER_STEP,
            //                                                       16384);

            //     sampleCount = 0;
            goPosition -= FINE_TUNE_CALIB_SPEED;
            // }
        }
        else
        {
            goPosition -= FINE_TUNE_CALIB_SPEED;
        }
        tb67h450.SetFocCurrentVector(goPosition, 2000);
        if (goPosition < 51200)
        {
            state = CALI_CALCULATING;
        }
        break;
    case CALI_CALCULATING:
        tb67h450.SetFocCurrentVector(0, 0);
        break;
    default:
        break;
    }
}

// 进行步距检测，通过后得到零点前的最后一步步数及零点后
// 第一步与零点的编码器距离


void EncoderCalibratorBase::TickMainLoop()
{
    int32_t dataI32;   // 每相鄰兩步數據差
    uint16_t dataU16;  // 對應细分数

    if (state != CALI_CALCULATING){  // 完成CALI
        return;
    }

    tb67h450base.Sleep();

    CalibrationDataCheck();

}

uint32_t EncoderCalibratorBase::CycleMod(uint32_t _a,uint32_t _b)
{
    return (_a + _b) % _b;
}

int32_t EncoderCalibratorBase::CycleSubtract(int32_t _a, int32_t _b, int32_t _cyc)
{
    int32_t sub_data;

    sub_data = _a - _b;
    if (sub_data > (_cyc >> 1))
        sub_data -= _cyc;
    if (sub_data < (-_cyc >> 1))
        sub_data += _cyc;
    return sub_data;
}


int32_t EncoderCalibratorBase::CycleAverage(int32_t _a,int32_t _b, int32_t _cyc)
{
    int32_t sub_data;
    int32_t avg_data;


    sub_data = _a - _b;
    avg_data = (_a + _b) >> 1; 

    if(fabs(sub_data) > (_cyc >>1)) //跳變判斷
    {
        if (avg_data >= (_cyc >> 1))
            avg_data -= (_cyc >> 1);
        else
            avg_data += (_cyc >> 1);
    } 

    return avg_data;
}