#include "tb67h450_stm32.h"
//#include "tim.h"
#include "sin_map.h"
#include "gpio.h"

void TB67H450::SetFocCurrentVector1(uint32_t _directionInCount, int32_t _current_mA)
{
        phaseB.sinMapPtr = (_directionInCount) & (0x000003FFF);
    phaseA.sinMapPtr = (phaseB.sinMapPtr + (256)) & (0x000003FFF);

    phaseA.sinMapData = sin_pi_m2[phaseA.sinMapPtr];
    phaseB.sinMapData = sin_pi_m2[phaseB.sinMapPtr];

    uint32_t dac_reg =abs(_current_mA);
    dac_reg = (uint32_t)(dac_reg*5083) >> 12;//将0到3300映射至0到4095
    dac_reg = (dac_reg) & (0x00000FFF);
    phaseA.dacValue12Bits =
           (uint32_t) (dac_reg * fabs(phaseA.sinMapData)) >> sin_pi_m2_dpiybit;
    phaseB.dacValue12Bits =
           (uint32_t) (dac_reg * fabs(phaseB.sinMapData)) >> sin_pi_m2_dpiybit;       

    SetTwoCoilsCurrent( phaseA.dacValue12Bits ,phaseB.dacValue12Bits );
    
    // A 相的電流控制 H / L 電位
    if(phaseA.sinMapData > 0)
        SetInputA(true,false);// A+ 為 High，A- 為 Low => 正方向
    else if(phaseA.sinMapData < 0) 
        SetInputA(false,true);// A+ 為 Low，A- 為 High => 反方向
    else
        SetInputA(true, true);    // A+、A- 都 High，等效於 Hi-Z 或剎車
    
    // B phase
    if(phaseB.sinMapData > 0)
        SetInputB(true,false);
    else if(phaseB.sinMapData < 0) 
        SetInputB(false,true);
    else
        SetInputB(true, true);   
}

void TB67H450::InitGpio()
{
    GPIO_InitTypeDef GPIO_InitStruct = {0};

    /* GPIO Ports Clock Enable */
    __HAL_RCC_GPIOD_CLK_ENABLE();
    __HAL_RCC_GPIOA_CLK_ENABLE();

    /*Configure GPIO pin Output Level */
    //HAL_GPIO_WritePin(CS_GPIO_Port, CS_Pin, GPIO_PIN_RESET);
    // 2025/05/18 未配置
    /*Configure Signal pins : PAPin PAPin PAPin PAPin */
}

void TB67H450::InitPwm()
{
   // MX_TIM2_Init();
}

void TB67H450::SetInputA(bool _statusAp, bool _statusAm)
{
   //_statusAp ? (GPIOA->BSRR =):(GPIOA->BRR =);
   //_statusAm ? (GPIOA->BSRR =):(GPIOA->BRR =);
}

void TB67H450::SetInputB(bool _statusBp, bool _statusBm)
{
   //_statusBp ? (GPIOA->BSRR =):(GPIOA->BRR =);
   //_statusBm ? (GPIOA->BSRR =):(GPIOA->BRR =);
}