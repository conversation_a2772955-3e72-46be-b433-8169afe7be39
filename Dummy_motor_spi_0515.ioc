#MicroXplorer Configuration settings - do not modify
CAD.formats=
CAD.pinconfig=
CAD.provider=
File.Version=6
GPIO.groupedBy=Group By Peripherals
KeepUserPlacement=false
Mcu.CPN=STM32F103C8T6
Mcu.Family=STM32F1
Mcu.IP0=NVIC
Mcu.IP1=RCC
Mcu.IP2=SPI1
Mcu.IP3=SYS
Mcu.IP4=TIM1
Mcu.IP5=TIM2
Mcu.IP6=TIM4
Mcu.IPNb=7
Mcu.Name=STM32F103C(8-B)Tx
Mcu.Package=LQFP48
Mcu.Pin0=PD0-OSC_IN
Mcu.Pin1=PD1-OSC_OUT
Mcu.Pin10=PB14
Mcu.Pin11=PB15
Mcu.Pin12=PA13
Mcu.Pin13=PA14
Mcu.Pin14=VP_SYS_VS_Systick
Mcu.Pin15=VP_TIM1_VS_ClockSourceINT
Mcu.Pin16=VP_TIM2_VS_ClockSourceINT
Mcu.Pin17=VP_TIM4_VS_ClockSourceINT
Mcu.Pin2=PA4
Mcu.Pin3=PA5
Mcu.Pin4=PA6
Mcu.Pin5=PA7
Mcu.Pin6=PB10
Mcu.Pin7=PB11
Mcu.Pin8=PB12
Mcu.Pin9=PB13
Mcu.PinsNb=18
Mcu.ThirdPartyNb=0
Mcu.UserConstants=
Mcu.UserName=STM32F103C8Tx
MxCube.Version=6.14.1
MxDb.Version=DB.6.0.141
NVIC.BusFault_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.DebugMonitor_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.ForceEnableDMAVector=true
NVIC.HardFault_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.MemoryManagement_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.NonMaskableInt_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.PendSV_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.PriorityGroup=NVIC_PRIORITYGROUP_4
NVIC.SVCall_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.SysTick_IRQn=true\:15\:0\:false\:false\:true\:false\:true\:false
NVIC.TIM1_UP_IRQn=true\:0\:0\:false\:false\:true\:true\:true\:true
NVIC.TIM4_IRQn=true\:0\:0\:false\:false\:true\:true\:true\:true
NVIC.UsageFault_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
PA13.Mode=Serial_Wire
PA13.Signal=SYS_JTMS-SWDIO
PA14.Mode=Serial_Wire
PA14.Signal=SYS_JTCK-SWCLK
PA4.GPIOParameters=GPIO_Speed,GPIO_PuPd,GPIO_Label
PA4.GPIO_Label=CS
PA4.GPIO_PuPd=GPIO_PULLDOWN
PA4.GPIO_Speed=GPIO_SPEED_FREQ_MEDIUM
PA4.Locked=true
PA4.Signal=GPIO_Output
PA5.Mode=Full_Duplex_Master
PA5.Signal=SPI1_SCK
PA6.Mode=Full_Duplex_Master
PA6.Signal=SPI1_MISO
PA7.Mode=Full_Duplex_Master
PA7.Signal=SPI1_MOSI
PB10.Locked=true
PB10.Signal=S_TIM2_CH3
PB11.Locked=true
PB11.Signal=S_TIM2_CH4
PB12.GPIOParameters=GPIO_Speed
PB12.GPIO_Speed=GPIO_SPEED_FREQ_HIGH
PB12.Locked=true
PB12.Signal=GPIO_Output
PB13.GPIOParameters=GPIO_Speed
PB13.GPIO_Speed=GPIO_SPEED_FREQ_HIGH
PB13.Locked=true
PB13.Signal=GPIO_Output
PB14.GPIOParameters=GPIO_Speed
PB14.GPIO_Speed=GPIO_SPEED_FREQ_HIGH
PB14.Locked=true
PB14.Signal=GPIO_Output
PB15.GPIOParameters=GPIO_Speed
PB15.GPIO_Speed=GPIO_SPEED_FREQ_HIGH
PB15.Locked=true
PB15.Signal=GPIO_Output
PD0-OSC_IN.Mode=HSE-External-Oscillator
PD0-OSC_IN.Signal=RCC_OSC_IN
PD1-OSC_OUT.Mode=HSE-External-Oscillator
PD1-OSC_OUT.Signal=RCC_OSC_OUT
PinOutPanel.RotationAngle=0
ProjectManager.AskForMigrate=true
ProjectManager.BackupPrevious=false
ProjectManager.CompilerLinker=GCC
ProjectManager.CompilerOptimize=6
ProjectManager.ComputerToolchain=false
ProjectManager.CoupleFile=true
ProjectManager.CustomerFirmwarePackage=
ProjectManager.DefaultFWLocation=true
ProjectManager.DeletePrevious=true
ProjectManager.DeviceId=STM32F103C8Tx
ProjectManager.FirmwarePackage=STM32Cube FW_F1 V1.8.6
ProjectManager.FreePins=false
ProjectManager.HalAssertFull=false
ProjectManager.HeapSize=0x200
ProjectManager.KeepUserCode=true
ProjectManager.LastFirmware=true
ProjectManager.LibraryCopy=1
ProjectManager.MainLocation=Core/Src
ProjectManager.NoMain=false
ProjectManager.PreviousToolchain=STM32CubeIDE
ProjectManager.ProjectBuild=false
ProjectManager.ProjectFileName=Dummy_motor_spi_0515.ioc
ProjectManager.ProjectName=Dummy_motor_spi_0515
ProjectManager.ProjectStructure=
ProjectManager.RegisterCallBack=
ProjectManager.StackSize=0x400
ProjectManager.TargetToolchain=STM32CubeIDE
ProjectManager.ToolChainLocation=
ProjectManager.UAScriptAfterPath=
ProjectManager.UAScriptBeforePath=
ProjectManager.UnderRoot=true
ProjectManager.functionlistsort=1-SystemClock_Config-RCC-false-HAL-false,2-MX_GPIO_Init-GPIO-false-HAL-true,3-MX_SPI1_Init-SPI1-false-HAL-true,4-MX_TIM1_Init-TIM1-false-HAL-true,5-MX_TIM2_Init-TIM2-false-HAL-true,6-MX_TIM4_Init-TIM4-false-HAL-true
RCC.ADCFreqValue=36000000
RCC.AHBFreq_Value=72000000
RCC.APB1CLKDivider=RCC_HCLK_DIV2
RCC.APB1Freq_Value=36000000
RCC.APB1TimFreq_Value=72000000
RCC.APB2Freq_Value=72000000
RCC.APB2TimFreq_Value=72000000
RCC.FCLKCortexFreq_Value=72000000
RCC.FamilyName=M
RCC.HCLKFreq_Value=72000000
RCC.IPParameters=ADCFreqValue,AHBFreq_Value,APB1CLKDivider,APB1Freq_Value,APB1TimFreq_Value,APB2Freq_Value,APB2TimFreq_Value,FCLKCortexFreq_Value,FamilyName,HCLKFreq_Value,MCOFreq_Value,PLLCLKFreq_Value,PLLMCOFreq_Value,PLLMUL,PLLSourceVirtual,SYSCLKFreq_VALUE,SYSCLKSource,TimSysFreq_Value,USBFreq_Value,VCOOutput2Freq_Value
RCC.MCOFreq_Value=72000000
RCC.PLLCLKFreq_Value=72000000
RCC.PLLMCOFreq_Value=36000000
RCC.PLLMUL=RCC_PLL_MUL9
RCC.PLLSourceVirtual=RCC_PLLSOURCE_HSE
RCC.SYSCLKFreq_VALUE=72000000
RCC.SYSCLKSource=RCC_SYSCLKSOURCE_PLLCLK
RCC.TimSysFreq_Value=72000000
RCC.USBFreq_Value=72000000
RCC.VCOOutput2Freq_Value=8000000
SH.S_TIM2_CH3.0=TIM2_CH3,PWM Generation3 CH3
SH.S_TIM2_CH3.ConfNb=1
SH.S_TIM2_CH4.0=TIM2_CH4,PWM Generation4 CH4
SH.S_TIM2_CH4.ConfNb=1
SPI1.BaudRatePrescaler=SPI_BAUDRATEPRESCALER_16
SPI1.CLKPhase=SPI_PHASE_2EDGE
SPI1.CLKPolarity=SPI_POLARITY_HIGH
SPI1.CalculateBaudRate=4.5 MBits/s
SPI1.DataSize=SPI_DATASIZE_16BIT
SPI1.Direction=SPI_DIRECTION_2LINES
SPI1.IPParameters=VirtualType,Mode,Direction,CalculateBaudRate,DataSize,BaudRatePrescaler,CLKPolarity,CLKPhase
SPI1.Mode=SPI_MODE_MASTER
SPI1.VirtualType=VM_MASTER
TIM1.ClockDivision=TIM_CLOCKDIVISION_DIV1
TIM1.IPParameters=Prescaler,Period,ClockDivision
TIM1.Period=9999
TIM1.Prescaler=71
TIM2.Channel-PWM\ Generation3\ CH3=TIM_CHANNEL_3
TIM2.Channel-PWM\ Generation4\ CH4=TIM_CHANNEL_4
TIM2.IPParameters=Period,Channel-PWM Generation3 CH3,Channel-PWM Generation4 CH4
TIM2.Period=1023
TIM4.IPParameters=Prescaler,Period
TIM4.Period=49
TIM4.Prescaler=71
VP_SYS_VS_Systick.Mode=SysTick
VP_SYS_VS_Systick.Signal=SYS_VS_Systick
VP_TIM1_VS_ClockSourceINT.Mode=Internal
VP_TIM1_VS_ClockSourceINT.Signal=TIM1_VS_ClockSourceINT
VP_TIM2_VS_ClockSourceINT.Mode=Internal
VP_TIM2_VS_ClockSourceINT.Signal=TIM2_VS_ClockSourceINT
VP_TIM4_VS_ClockSourceINT.Mode=Internal
VP_TIM4_VS_ClockSourceINT.Signal=TIM4_VS_ClockSourceINT
board=custom
